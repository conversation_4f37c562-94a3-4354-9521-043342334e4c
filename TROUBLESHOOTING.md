# 🔧 Troubleshooting Guide - FM Challenger

## 🍪 Problemi Cookie Banner

### Errore: "SyntaxError: Unexpected token 'a', 'accepted' is not valid JSON"

**Causa**: Dati corrotti nel localStorage del browser per il consenso dei cookie.

**Soluzioni**:

#### 1. Soluzione Automatica (Implementata)
Il sistema ora rileva automaticamente i dati corrotti e li pulisce. Controlla la console del browser per messaggi di debug.

#### 2. Soluzione Manuale - Browser
1. Apri gli strumenti sviluppatore (F12)
2. Vai alla tab "Application" o "Storage"
3. <PERSON><PERSON><PERSON> "Local Storage" → il tuo dominio
4. Elimina la chiave `cookie-consent`
5. Ricarica la pagina

#### 3. Soluzione Manuale - Console
1. Apri la console del browser (F12 → Console)
2. Esegui: `localStorage.removeItem('cookie-consent')`
3. Ricarica la pagina

#### 4. Script di Debug
Carica lo script di debug in `/public/debug-cookies.js`:

```javascript
// Nella console del browser:
debugCookieConsent();    // Analizza i dati
clearCookieConsent();    // Pulisce i dati corrotti
setCookieConsent(true, false); // Imposta consenso manualmente
```

### Prevenzione
- Il sistema ora valida i dati prima del parsing
- Gestione errori migliorata con try/catch
- Controlli di integrità dei dati
- Logging dettagliato per debug

## 🚀 Problemi di Build

### Errore: "useSearchParams() should be wrapped in a suspense boundary"

**Causa**: Hook useSearchParams utilizzato senza Suspense boundary.

**Soluzione**: Già risolto nei componenti:
- `components/GoogleAnalytics.jsx`
- `app/auth/error/page.jsx`

### Errore: "Cannot serialize key 'parse' in parser"

**Causa**: Warning ESLint, non blocca il build.

**Soluzione**: Ignorabile, il build completa con successo.

## 🎨 Problemi di Styling

### Colori non applicati correttamente

**Verifica**:
1. Controlla `tailwind.config.mjs` per i colori personalizzati
2. Assicurati che le classi CSS siano corrette
3. Verifica che TailwindCSS sia configurato correttamente

### Layout responsive non funziona

**Verifica**:
1. Controlla le classi responsive (sm:, md:, lg:, xl:)
2. Testa su diversi dispositivi/dimensioni
3. Verifica il viewport meta tag nel layout

## 📱 Problemi PWA

### Manifest.json non caricato

**Verifica**:
1. File presente in `/public/manifest.json`
2. Link corretto nel layout: `<link rel="manifest" href="/manifest.json" />`
3. Sintassi JSON valida

### Icone PWA mancanti

**Aggiungi**:
- `/public/favicon.ico`
- `/public/apple-touch-icon.png`
- `/public/icon-192.png`
- `/public/icon-512.png`

## 📂 Problemi Filtri Sfide Salvate

### Filtri non funzionano correttamente

**Sintomi**:
- Sfide completate mostrano tutte le sfide
- Sfide archiviate mostrano tutte le sfide
- Sfide attive non filtrano correttamente

**Causa**: Logica di filtraggio API errata

**Soluzione**: Verificare la logica in `app/api/challenges/filtered/route.js`

**Debug**:
1. Controlla i valori boolean nel database Supabase
2. Usa lo script `debug-filters.js` nella console
3. Verifica i log dell'API per errori

**Logica Corretta**:
- **Attive**: `!archived && !(tutti_obiettivi_completati)`
- **Completate**: `!archived && tutti_obiettivi_completati`
- **Archiviate**: `archived === true`

## 🔍 Problemi SEO

### Sitemap non generata

**Verifica**:
1. File `app/sitemap.js` presente
2. Funzione export default corretta
3. URL base configurato correttamente

### Meta tags non visibili

**Verifica**:
1. Metadata nel `app/layout.js`
2. Open Graph tags configurati
3. Twitter Cards configurati

## 📊 Problemi Analytics

### Google Analytics non traccia

**Verifica**:
1. `NEXT_PUBLIC_GA_MEASUREMENT_ID` configurato
2. Consenso cookie dato per analytics
3. Script GA caricato correttamente
4. Console browser per errori

### Consent Mode non funziona

**Verifica**:
1. Google Consent Mode v2 inizializzato
2. Banner cookie funzionante
3. Aggiornamenti consenso inviati a GA

## 🗄️ Problemi Database

### Errori Supabase

**Verifica**:
1. Variabili ambiente configurate
2. Tabelle database create
3. RLS policies configurate
4. Connessione di rete

### Autenticazione non funziona

**Verifica**:
1. NextAuth configurato correttamente
2. Provider OAuth configurati
3. Callback URLs corretti
4. Variabili ambiente complete

## 🛠️ Debug Generale

### Console Errors

**Controlla sempre**:
1. Console browser per errori JavaScript
2. Network tab per richieste fallite
3. Application tab per localStorage/cookies
4. Performance tab per problemi di caricamento

### Logging

**Aggiungi logging**:
```javascript
console.log('Debug info:', data);
console.error('Error:', error);
console.warn('Warning:', warning);
```

### Environment Variables

**Verifica**:
1. File `.env.local` presente
2. Variabili corrette e complete
3. Restart server dopo modifiche
4. Variabili pubbliche con prefisso `NEXT_PUBLIC_`

## 📞 Supporto

Se i problemi persistono:

1. **Controlla i log**: Console browser e server
2. **Verifica la configurazione**: Variabili ambiente e file config
3. **Testa in incognito**: Per escludere cache/estensioni
4. **Controlla la documentazione**: README.md e file di progetto
5. **Cerca nei file**: Usa la ricerca per trovare configurazioni specifiche

### File di Configurazione Chiave

- `tailwind.config.mjs` - Configurazione styling
- `app/layout.js` - Layout principale e metadata
- `next.config.js` - Configurazione Next.js (se presente)
- `vercel.json` - Configurazione deployment
- `.env.local` - Variabili ambiente

### Comandi Utili

```bash
# Pulisci cache e reinstalla
rm -rf .next node_modules package-lock.json
npm install

# Build di test
npm run build

# Lint check
npm run lint

# Sviluppo con debug
npm run dev
```
