'use client';

import { useLanguage } from '../lib/LanguageContext';

export default function LanguageSelector() {
  const { language, changeLanguage } = useLanguage();

  return (
    <div className="flex items-center space-x-2">
      <button
        onClick={() => changeLanguage('it')}
        className={`px-2 py-1 rounded-md ${
          language === 'it'
            ? 'bg-accent text-white'
            : 'bg-dropdownBg text-textSecondary hover:bg-bgBox'
        }`}
        aria-label="Cambia lingua in Italiano"
      >
        🇮🇹 IT
      </button>
      <button
        onClick={() => changeLanguage('en')}
        className={`px-2 py-1 rounded-md ${
          language === 'en'
            ? 'bg-accent text-white'
            : 'bg-dropdownBg text-textSecondary hover:bg-bgBox'
        }`}
        aria-label="Change language to English"
      >
        🇬🇧 EN
      </button>
    </div>
  );
}
