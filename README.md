# 🏆 FM Challenger - Football Manager Challenge Generator

Una web app moderna e responsive per generare sfide casuali per Football Manager, con supporto multilingue, autenticazione utente e integrazione Supabase.

## ✨ Funzionalità Principali

### 🎮 Generatore di Sfide
- **Selezione Difficoltà**: Scegli il livello di difficoltà per squadre e obiettivi
- **Filtri Continenti**: Filtra le squadre per continente
- **Sistema di Lock**: Blocca elementi specifici durante la rigenerazione
- **Salvataggio Sfide**: Salva le tue sfide preferite (utenti registrati)

### 🌍 Multilingue
- **Italiano** e **Inglese** supportati
- Contenuti dinamici dal database Supabase
- Interfaccia completamente localizzata

### 👤 Autenticazione
- **Email/Password** con crittografia bcrypt
- **Google OAuth** integrato
- **Profilo Utente** con gestione sfide salvate
- **Stato Completamento** per ogni sfida

### 🎨 Design Moderno
- **Tema Verde Calcio**: Palette colori ispirata al mondo del calcio
- **Responsive Design**: Ottimizzato per desktop e mobile
- **Animazioni Fluide**: Transizioni e hover effects
- **Glass Morphism**: Effetti di trasparenza moderni

### 🍪 Privacy & Compliance
- **Cookie Banner**: Conforme GDPR con Google Consent Mode v2
- **Gestione Consensi**: Controllo granulare dei cookie
- **Privacy Policy** e **Terms of Service** integrati

### 📱 Social & SEO
- **Social Links**: Facebook, Instagram, X (Twitter), Reddit
- **SEO Ottimizzato**: Meta tags, Open Graph, Twitter Cards
- **Sitemap Dinamica**: Generazione automatica sitemap.xml
- **Structured Data**: Schema.org per migliore indicizzazione
- **PWA Ready**: Manifest.json per installazione come app

## 🛠️ Stack Tecnologico

- **Framework**: Next.js 15.1.8 (App Router)
- **Styling**: TailwindCSS con tema personalizzato
- **Database**: Supabase (PostgreSQL)
- **Autenticazione**: NextAuth.js + Supabase Auth
- **Analytics**: Google Analytics 4 con Consent Mode v2
- **Deployment**: Vercel (consigliato)

## 🚀 Installazione e Setup

### Prerequisiti
- Node.js 18+
- npm/yarn/pnpm
- Account Supabase
- Account Google (per OAuth)

### 1. Clona il Repository
```bash
git clone https://github.com/tuousername/fm-challenger-frontend.git
cd fm-challenger-frontend
```

### 2. Installa le Dipendenze
```bash
npm install
# oppure
yarn install
```

### 3. Configura le Variabili d'Ambiente
Crea un file `.env.local`:

```env
# Supabase
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# NextAuth
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Google Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
```

### 4. Setup Database Supabase
Esegui le migrazioni SQL in Supabase:

```sql
-- Tabella teams
CREATE TABLE difficulty_teams (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  difficulty VARCHAR(50) NOT NULL,
  continent VARCHAR(100),
  created_at TIMESTAMP DEFAULT NOW()
);

-- Tabella challenges
CREATE TABLE difficulty_challenges (
  id SERIAL PRIMARY KEY,
  category VARCHAR(100) NOT NULL,
  difficulty VARCHAR(50) NOT NULL,
  text_it TEXT NOT NULL,
  text_en TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Tabella users
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255),
  name VARCHAR(255),
  image_url TEXT,
  provider VARCHAR(50) DEFAULT 'email',
  created_at TIMESTAMP DEFAULT NOW()
);

-- Tabella saved_challenges
CREATE TABLE saved_challenges (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  team_name VARCHAR(255) NOT NULL,
  team_difficulty VARCHAR(50) NOT NULL,
  challenges JSONB NOT NULL,
  completed BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW()
);
```

### 5. Avvia il Server di Sviluppo
```bash
npm run dev
```

Apri [http://localhost:3000](http://localhost:3000) nel browser.

## 📁 Struttura del Progetto

```
fm-challenger-frontend/
├── app/                    # App Router (Next.js 13+)
│   ├── api/               # API Routes
│   ├── auth/              # Pagine autenticazione
│   ├── profile/           # Profilo utente
│   ├── privacy/           # Privacy Policy
│   ├── terms/             # Terms of Service
│   ├── contact/           # Contatti
│   ├── layout.js          # Layout principale
│   ├── page.js            # Homepage
│   ├── sitemap.js         # Sitemap dinamica
│   └── robots.js          # Robots.txt
├── components/            # Componenti React
│   ├── CookieBanner.jsx   # Banner cookie GDPR
│   ├── SocialLinks.jsx    # Links social media
│   ├── GoogleAnalytics.jsx # GA4 + Consent Mode
│   └── ...
├── lib/                   # Utilities e configurazioni
│   ├── supabase.js        # Client Supabase
│   └── LanguageContext.js # Context multilingue
├── public/                # Asset statici
│   ├── manifest.json      # PWA Manifest
│   └── ...
└── tailwind.config.mjs    # Configurazione TailwindCSS
```

## 🎨 Personalizzazione Tema

Il tema utilizza una palette verde ispirata al calcio. Puoi personalizzare i colori in `tailwind.config.mjs`:

```javascript
colors: {
  primary: "#1B4332",      // Verde scuro calcio
  accent: "#2D6A4F",       // Verde medio
  secondary: "#40916C",    // Verde chiaro
  tertiary: "#52B788",     // Verde lime
  bgMain: "#081C15",       // Background principale
  bgBox: "#1B4332",        // Background card
  // ... altri colori
}
```

## 🔧 Configurazione Avanzata

### Google Analytics 4 + Consent Mode v2
Il progetto include una configurazione completa di GA4 con Consent Mode v2:

```javascript
// Configurazione automatica in GoogleAnalytics.jsx
gtag('consent', 'default', {
  'analytics_storage': 'denied',
  'ad_storage': 'denied',
  'ad_user_data': 'denied',
  'ad_personalization': 'denied'
});
```

### Cookie Banner GDPR
Banner completamente personalizzabile con gestione granulare dei consensi:
- Cookie necessari (sempre attivi)
- Cookie analitici (opzionali)
- Cookie pubblicitari (opzionali)

## 📱 PWA (Progressive Web App)

L'app è configurata come PWA con:
- **Manifest.json** per installazione
- **Service Worker** (da implementare)
- **Offline Support** (da implementare)

## 🚀 Deployment

### Vercel (Consigliato)
1. Connetti il repository a Vercel
2. Configura le variabili d'ambiente
3. Deploy automatico ad ogni push

### Altri Provider
L'app è compatibile con qualsiasi provider che supporta Next.js:
- Netlify
- Railway
- DigitalOcean App Platform

## 🤝 Contribuire

1. Fork del progetto
2. Crea un branch per la feature (`git checkout -b feature/AmazingFeature`)
3. Commit delle modifiche (`git commit -m 'Add some AmazingFeature'`)
4. Push al branch (`git push origin feature/AmazingFeature`)
5. Apri una Pull Request

## 📄 Licenza

Questo progetto è sotto licenza MIT. Vedi il file `LICENSE` per dettagli.

## 📞 Supporto

- **Email**: <EMAIL>
- **Issues**: [GitHub Issues](https://github.com/tuousername/fm-challenger-frontend/issues)
- **Documentazione**: [Wiki del progetto](https://github.com/tuousername/fm-challenger-frontend/wiki)

---

Sviluppato con ❤️ per la community di Football Manager
