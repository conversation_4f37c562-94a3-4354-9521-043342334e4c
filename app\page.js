'use client';

import { useState } from 'react';
import Introduction from '../components/Introduction';
import SelectChallenge from '../components/SelectChallenge';
import ShowChallenge from '../components/ShowChallenge';
import { getRandomTeam, getChallenges, getRandomChallenge, supabase } from '../lib/supabase';
import { useLanguage } from '../lib/LanguageContext';

export default function Home() {
  const { t, language } = useLanguage();
  const [team, setTeam] = useState(null);
  const [challenges, setChallenges] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedDifficulties, setSelectedDifficulties] = useState({
    team: '',
    challenge: ''
  });

  // Funzione per tracciare eventi GA
  const trackEvent = (eventName, eventParams = {}) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', eventName, eventParams);
    }
  };

  const generateChallenge = async (teamDifficulty, challengeDifficulty, lang = 'it', continents = []) => {
    setLoading(true);
    setError(null);

    // Traccia l'evento di generazione sfida
    trackEvent('generate_challenge', {
      team_difficulty: teamDifficulty,
      challenge_difficulty: challengeDifficulty,
      language: lang
    });

    // Salva le difficoltà selezionate per poterle riutilizzare durante la rigenerazione
    setSelectedDifficulties({
      team: teamDifficulty,
      challenge: challengeDifficulty,
      continents: continents
    });

    try {
      console.log(`Generating challenge with team difficulty: ${teamDifficulty}, challenge difficulty: ${challengeDifficulty}, language: ${lang}, continents: ${continents.join(', ')}`);

      // Get random team based on difficulty and continents
      const randomTeam = await getRandomTeam(teamDifficulty, continents);

      // Get random challenges based on difficulty
      const randomChallenges = await getChallenges(challengeDifficulty, lang);

      if (!randomTeam) {
        throw new Error(t.errorTeam);
      }

      if (!randomChallenges || Object.keys(randomChallenges).length === 0) {
        throw new Error(t.errorChallenge);
      }

      setTeam(randomTeam);
      setChallenges(randomChallenges);
    } catch (err) {
      console.error('Error generating challenge:', err);
      setError(err.message || t.errorGeneric);
    } finally {
      setLoading(false);
    }
  };

  // Stato per tenere traccia degli elementi bloccati
  const [lockedItems, setLockedItems] = useState({
    team: false,
    obiettivi: false,
    rosa: false,
    tattica: false,
  });

  const regenerateChallenge = async (currentLockedItems) => {
    setLoading(true);
    setError(null);

    // Traccia l'evento di rigenerazione sfida
    trackEvent('regenerate_challenge', {
      team_difficulty: selectedDifficulties.team,
      challenge_difficulty: selectedDifficulties.challenge,
      language: language
    });

    // Aggiorniamo lo stato dei lock con quello corrente
    setLockedItems(currentLockedItems);

    try {
      let newTeam = team;
      let newChallenges = { ...challenges };

      // If team is not locked, get a new random team
      if (!currentLockedItems.team && team) {
        // Usa la difficoltà e i continenti salvati in precedenza
        const continents = selectedDifficulties.continents || [];
        const randomTeam = await getRandomTeam(selectedDifficulties.team, continents);
        if (randomTeam) {
          newTeam = randomTeam;
        }
      }

      // For each challenge category that is not locked, get a new random challenge
      for (const category of ['obiettivi', 'rosa', 'tattica']) {
        if (!currentLockedItems[category] && challenges && challenges[category]) {
          try {
            // Utilizziamo la funzione esistente per ottenere una nuova sfida casuale
            const newChallenge = await getRandomChallenge(
              selectedDifficulties.challenge,
              category,
              language
            );

            if (newChallenge && newChallenge.id !== challenges[category].id) {
              newChallenges[category] = newChallenge;
            }
          } catch (err) {
            console.error(`Error regenerating challenge for category ${category}:`, err);
          }
        }
      }

      setTeam(newTeam);
      setChallenges(newChallenges);
    } catch (err) {
      console.error('Error regenerating challenge:', err);
      setError(err.message || t.errorGeneric);
    } finally {
      setLoading(false);
    }
  };

  const shareChallenge = async () => {
    // Traccia l'evento di condivisione sfida
    trackEvent('share_challenge', {
      team_name: team?.name || 'unknown',
      language: language
    });

    // Resto del codice esistente...
  };

  return (
    <div className="min-h-screen bg-bgMain text-textPrimary pt-4 pb-8 px-4 md:pt-6 md:pb-12 md:px-8">
      <div className="max-w-4xl mx-auto">
        <Introduction />

        <SelectChallenge onGenerateChallenge={generateChallenge} />

        {loading && (
          <div className="text-center py-8">
            <p className="text-xl">{t.loading}</p>
          </div>
        )}

        {error && (
          <div className="bg-red-900/20 border border-red-500/50 text-red-200 p-4 rounded-md mb-6 max-w-md mx-auto">
            <p>{error}</p>
          </div>
        )}

        {team && challenges && !loading && (
          <ShowChallenge
            team={team}
            challenges={challenges}
            onRegenerate={regenerateChallenge}
            initialLockedState={lockedItems}
          />
        )}

        <footer className="mt-12 text-center text-textSecondary text-sm">
          <p>FM Challenger &copy; {new Date().getFullYear()} - {t.footer}</p>
        </footer>
      </div>
    </div>
  );
}

