import { NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

export async function GET(request) {
  try {
    // Verifica che l'utente sia autenticato
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Non autorizzato" },
        { status: 401 }
      );
    }

    // Ottieni l'ID dell'utente dal database
    const { data: userData, error: userError } = await supabase
      .from("app_users")
      .select("id")
      .eq("email", session.user.email)
      .single();

    if (userError || !userData) {
      console.error("Errore nel recupero dell'utente:", userError);
      return NextResponse.json(
        { error: "Utente non trovato" },
        { status: 404 }
      );
    }

    // Recupera le sfide salvate dell'utente
    const { data, error } = await supabase
      .from("saved_challenges")
      .select("*")
      .eq("user_id", userData.id)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Errore nel recupero delle sfide:", error);
      return NextResponse.json(
        { error: "Errore nel recupero delle sfide" },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { challenges: data },
      { status: 200 }
    );
  } catch (error) {
    console.error("Errore nel recupero delle sfide:", error);
    return NextResponse.json(
      { error: "Errore nel recupero delle sfide" },
      { status: 500 }
    );
  }
}
