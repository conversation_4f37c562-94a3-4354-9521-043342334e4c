import { Inter } from "next/font/google";
import "./globals.css";
import { LanguageProvider } from "../lib/LanguageContext";
import SessionProvider from "../components/SessionProvider";
import Topbar from "../components/Topbar";
import { Toaster } from 'react-hot-toast';

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

export const metadata = {
  title: "FM Challenger - Create your challenge for FM",
  description: "Generate random challenges for Football Manager. Select difficulty levels and get a random team with custom objectives.",
  keywords: "Football Manager, FM, challenge, random, generator, football, soccer, game",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#0e3169" />
      </head>
      <body className={`${inter.variable} font-inter antialiased`}>
        <SessionProvider>
          <LanguageProvider>
            <Toaster position="top-center" />
            <Topbar />
            <main>
              {children}
            </main>
          </LanguageProvider>
        </SessionProvider>
      </body>
    </html>
  );
}

