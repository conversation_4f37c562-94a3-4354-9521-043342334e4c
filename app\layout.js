import { Inter } from "next/font/google";
import "./globals.css";
import { LanguageProvider } from "../lib/LanguageContext";
import SessionProvider from "../components/SessionProvider";
import Topbar from "../components/Topbar";
import CookieBanner from "../components/CookieBanner";
import GoogleAnalytics from "../components/GoogleAnalytics";
import { Toaster } from 'react-hot-toast';

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

export const metadata = {
  title: "FM Challenger - Create your challenge for FM",
  description: "Generate random challenges for Football Manager. Select difficulty levels and get a random team with custom objectives.",
  keywords: "Football Manager, FM, challenge, random, generator, football, soccer, game, tactics, squad, objectives",
  authors: [{ name: "FM Challenger Team" }],
  creator: "FM Challenger",
  publisher: "FM Challenger",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://fmchallenger.com'), // Sostituisci con il tuo dominio
  alternates: {
    canonical: '/',
    languages: {
      'it-IT': '/it',
      'en-US': '/en',
    },
  },
  openGraph: {
    title: "FM Challenger - Create your challenge for FM",
    description: "Generate random challenges for Football Manager. Select difficulty levels and get a random team with custom objectives.",
    url: 'https://fmchallenger.com',
    siteName: 'FM Challenger',
    images: [
      {
        url: '/og-image.jpg', // Aggiungi questa immagine in public/
        width: 1200,
        height: 630,
        alt: 'FM Challenger - Football Manager Challenge Generator',
      },
    ],
    locale: 'it_IT',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "FM Challenger - Create your challenge for FM",
    description: "Generate random challenges for Football Manager. Select difficulty levels and get a random team with custom objectives.",
    images: ['/og-image.jpg'],
    creator: '@fmchallenger',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#2D6A4F" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "WebApplication",
              "name": "FM Challenger",
              "description": "Generate random challenges for Football Manager. Select difficulty levels and get a random team with custom objectives.",
              "url": "https://fmchallenger.com",
              "applicationCategory": "GameApplication",
              "operatingSystem": "Web",
              "offers": {
                "@type": "Offer",
                "price": "0",
                "priceCurrency": "EUR"
              },
              "author": {
                "@type": "Organization",
                "name": "FM Challenger Team"
              }
            })
          }}
        />
      </head>
      <body className={`${inter.variable} font-inter antialiased`}>
        <GoogleAnalytics GA_MEASUREMENT_ID="G-XXXXXXXXXX" />
        <SessionProvider>
          <LanguageProvider>
            <Toaster position="top-center" />
            <Topbar />
            <main>
              {children}
            </main>
            <CookieBanner />
          </LanguageProvider>
        </SessionProvider>
      </body>
    </html>
  );
}

