import NextAuth from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import { supabase } from "@/lib/supabase";
import bcrypt from "bcryptjs";

const PLACEHOLDER_IMAGE = "https://ui-avatars.com/api/?background=0D8ABC&color=fff";

export const authOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // Cerca l'utente nel database
          const { data: user, error } = await supabase
            .from("app_users")
            .select("*")
            .eq("email", credentials.email)
            .single();

          if (error || !user) {
            return null;
          }

          // Verifica la password
          const passwordMatch = await bcrypt.compare(credentials.password, user.password);
          if (!passwordMatch) {
            return null;
          }

          return {
            id: user.id,
            name: user.username,
            email: user.email,
            image: user.image,
            role: user.role,
          };
        } catch (error) {
          console.error("Auth error:", error);
          return null;
        }
      }
    })
  ],
  callbacks: {
    async signIn({ user, account, profile }) {
      if (account.provider === "google") {
        try {
          // Controlla se l'utente esiste già
          const { data: existingUser } = await supabase
            .from("app_users")
            .select("*")
            .eq("email", profile.email)
            .single();

          if (!existingUser) {
            // Crea un nuovo utente
            await supabase.from("app_users").insert([
              {
                username: profile.name,
                email: profile.email,
                image: profile.picture,
                provider: "google",
                role: "utente"
              }
            ]);
          }
        } catch (error) {
          console.error("Error during Google sign in:", error);
        }
      }
      return true;
    },
    async jwt({ token, user, account }) {
      // Aggiungi dati personalizzati al token
      if (user) {
        token.role = user.role;
      }
      return token;
    },
    async session({ session, token }) {
      // Passa i dati dal token alla sessione
      if (token) {
        session.user.role = token.role;
      }
      return session;
    }
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
  },
  secret: process.env.NEXTAUTH_SECRET || "your-secret-key",
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
