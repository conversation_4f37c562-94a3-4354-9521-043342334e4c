import { NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

export async function POST(request) {
  try {
    // Verifica che l'utente sia autenticato
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Non autorizzato" },
        { status: 401 }
      );
    }

    // Ottieni i dati della richiesta
    const { challengeId, field, value } = await request.json();

    if (!challengeId || !field) {
      return NextResponse.json(
        { error: "Da<PERSON> mancanti" },
        { status: 400 }
      );
    }

    // Verifica che il campo sia valido
    const validFields = ['objective_completed', 'squad_completed', 'tactics_completed'];
    if (!validFields.includes(field)) {
      return NextResponse.json(
        { error: "Campo non valido" },
        { status: 400 }
      );
    }

    // Ottieni l'ID dell'utente dal database
    const { data: userData, error: userError } = await supabase
      .from("app_users")
      .select("id")
      .eq("email", session.user.email)
      .single();

    if (userError || !userData) {
      console.error("Errore nel recupero dell'utente:", userError);
      return NextResponse.json(
        { error: "Utente non trovato" },
        { status: 404 }
      );
    }

    // Verifica che la sfida appartenga all'utente
    const { data: challengeData, error: challengeError } = await supabase
      .from("saved_challenges")
      .select("id")
      .eq("id", challengeId)
      .eq("user_id", userData.id)
      .single();

    if (challengeError || !challengeData) {
      console.error("Errore nel recupero della sfida:", challengeError);
      return NextResponse.json(
        { error: "Sfida non trovata o non autorizzata" },
        { status: 404 }
      );
    }

    // Aggiorna lo stato di completamento
    const updateData = {};
    updateData[field] = value;

    const { error: updateError } = await supabase
      .from("saved_challenges")
      .update(updateData)
      .eq("id", challengeId);

    if (updateError) {
      console.error("Errore nell'aggiornamento della sfida:", updateError);
      return NextResponse.json(
        { error: "Errore nell'aggiornamento della sfida" },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { message: "Sfida aggiornata con successo" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Errore nell'aggiornamento della sfida:", error);
    return NextResponse.json(
      { error: "Errore nell'aggiornamento della sfida" },
      { status: 500 }
    );
  }
}
