'use client';

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useSession, signOut } from 'next-auth/react';
import { useLanguage } from '@/lib/LanguageContext';

export default function Topbar() {
  const { data: session, status } = useSession();
  const { t } = useLanguage();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const handleSignOut = () => {
    signOut({ callbackUrl: '/' });
  };

  return (
    <nav className="bg-primary py-3 px-4 shadow-md">
      <div className="container mx-auto flex justify-between items-center">
        <Link href="/" className="text-white font-bold text-xl">
          FM Challenger
        </Link>

        <div className="flex items-center">
          {status === 'authenticated' ? (
            <div className="relative">
              <button
                onClick={toggleMenu}
                className="flex items-center space-x-2 text-white"
                aria-expanded={isMenuOpen}
                aria-haspopup="true"
              >
                {session.user.image ? (
                  <Image
                    src={session.user.image}
                    alt={session.user.name || 'User'}
                    width={32}
                    height={32}
                    className="rounded-full"
                  />
                ) : (
                  <div className="w-8 h-8 bg-accent rounded-full flex items-center justify-center text-white">
                    {session.user.name?.charAt(0) || 'U'}
                  </div>
                )}
                <span className="hidden md:inline">{session.user.name}</span>
              </button>

              {isMenuOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-dropdownBg rounded-md shadow-lg py-1 z-10">
                  <Link
                    href="/profile"
                    className="block px-4 py-2 text-sm text-textPrimary hover:bg-bgBox"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {t.profile}
                  </Link>
                  <button
                    onClick={handleSignOut}
                    className="block w-full text-left px-4 py-2 text-sm text-textPrimary hover:bg-bgBox"
                  >
                    Logout
                  </button>
                </div>
              )}
            </div>
          ) : (
            <Link
              href="/auth/signin"
              className="text-white hover:text-gray-200 transition-colors"
            >
              Login
            </Link>
          )}
        </div>
      </div>
    </nav>
  );
}


