import { NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

export async function POST(request) {
  try {
    // Verifica che l'utente sia autenticato
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Non autorizzato" },
        { status: 401 }
      );
    }

    // Ottieni i dati della sfida
    const { team, challenges } = await request.json();

    if (!team || !challenges) {
      return NextResponse.json(
        { error: "<PERSON><PERSON> mancanti" },
        { status: 400 }
      );
    }

    // Ottieni l'ID dell'utente dal database
    const { data: userData, error: userError } = await supabase
      .from("app_users")
      .select("id")
      .eq("email", session.user.email)
      .single();

    if (userError || !userData) {
      console.error("Errore nel recupero dell'utente:", userError);
      return NextResponse.json(
        { error: "Utente non trovato" },
        { status: 404 }
      );
    }

    // Salva la sfida nel database
    const { data, error } = await supabase
      .from("saved_challenges")
      .insert([
        {
          user_id: userData.id,
          team_name: team.name,
          team_country: team.country,
          team_league: team.league,
          team_logo: team.logo,
          objective_text: challenges.obiettivi ? challenges.obiettivi.text : "",
          squad_text: challenges.rosa ? challenges.rosa.text : "",
          tactics_text: challenges.tattica ? challenges.tattica.text : "",
        }
      ])
      .select();

    if (error) {
      console.error("Errore nel salvataggio della sfida:", error);
      return NextResponse.json(
        { error: "Errore nel salvataggio della sfida" },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { message: "Sfida salvata con successo", id: data[0].id },
      { status: 201 }
    );
  } catch (error) {
    console.error("Errore nel salvataggio della sfida:", error);
    return NextResponse.json(
      { error: "Errore nel salvataggio della sfida" },
      { status: 500 }
    );
  }
}
