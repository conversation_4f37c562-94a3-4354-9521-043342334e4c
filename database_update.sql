-- Aggiornamento database per supportare l'archiviazione delle sfide
-- Esegui questo script nel tuo database Supabase

-- Aggiungi il campo 'archived' alla tabella saved_challenges
ALTER TABLE saved_challenges 
ADD COLUMN IF NOT EXISTS archived BOOLEAN DEFAULT FALSE;

-- Aggiungi un indice per migliorare le performance delle query filtrate
CREATE INDEX IF NOT EXISTS idx_saved_challenges_archived 
ON saved_challenges(user_id, archived);

-- Aggiungi un indice composto per le sfide completate
CREATE INDEX IF NOT EXISTS idx_saved_challenges_completed 
ON saved_challenges(user_id, objective_completed, squad_completed, tactics_completed, archived);

-- Commenti per documentazione
COMMENT ON COLUMN saved_challenges.archived IS 'Indica se la sfida è stata archiviata dall''utente';

-- Verifica che la struttura sia corretta
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'saved_challenges' 
ORDER BY ordinal_position;
