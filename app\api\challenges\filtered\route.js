import { NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

export async function GET(request) {
  try {
    // Verifica che l'utente sia autenticato
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json(
        { error: "Non autorizzato" },
        { status: 401 }
      );
    }

    // Ottieni i parametri di query
    const { searchParams } = new URL(request.url);
    const filter = searchParams.get('filter') || 'active'; // active, completed, archived

    // Ottieni l'ID dell'utente dal database
    const { data: userData, error: userError } = await supabase
      .from("app_users")
      .select("id")
      .eq("email", session.user.email)
      .single();

    if (userError || !userData) {
      console.error("Errore nel recupero dell'utente:", userError);
      return NextResponse.json(
        { error: "Utente non trovato" },
        { status: 404 }
      );
    }

    // Costruisci la query base
    let query = supabase
      .from("saved_challenges")
      .select("*")
      .eq("user_id", userData.id);

    // Applica i filtri
    switch (filter) {
      case 'completed':
        // Sfide completate: tutti e 3 gli obiettivi completati e non archiviate
        query = query
          .eq('objective_completed', true)
          .eq('squad_completed', true)
          .eq('tactics_completed', true)
          .eq('archived', false);
        break;
      
      case 'archived':
        // Sfide archiviate
        query = query.eq('archived', true);
        break;
      
      case 'active':
      default:
        // Sfide attive: non archiviate e non completate
        query = query
          .eq('archived', false)
          .or('objective_completed.is.null,objective_completed.eq.false,squad_completed.is.null,squad_completed.eq.false,tactics_completed.is.null,tactics_completed.eq.false');
        break;
    }

    // Ordina per data di creazione (più recenti prima)
    query = query.order("created_at", { ascending: false });

    const { data, error } = await query;

    if (error) {
      console.error("Errore nel recupero delle sfide:", error);
      return NextResponse.json(
        { error: "Errore nel recupero delle sfide" },
        { status: 500 }
      );
    }

    // Per le sfide attive, filtra manualmente quelle non completate
    let filteredData = data;
    if (filter === 'active') {
      filteredData = data.filter(challenge => 
        !challenge.archived && 
        !(challenge.objective_completed && challenge.squad_completed && challenge.tactics_completed)
      );
    }

    return NextResponse.json(
      { challenges: filteredData },
      { status: 200 }
    );
  } catch (error) {
    console.error("Errore nel recupero delle sfide:", error);
    return NextResponse.json(
      { error: "Errore nel recupero delle sfide" },
      { status: 500 }
    );
  }
}
