{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "regions": ["fra1"], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/sitemap.xml", "headers": [{"key": "Content-Type", "value": "application/xml"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}]}