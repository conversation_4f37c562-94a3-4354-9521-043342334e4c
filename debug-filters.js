// Script di debug per testare la logica di filtraggio delle sfide
// Esegui questo nella console del browser per verificare la logica

function debugChallengeFilters() {
  console.log('=== Debug Filtri Sfide ===');
  
  // Dati di esempio per testare la logica
  const sampleChallenges = [
    {
      id: 1,
      team_name: "Manchester United",
      archived: false,
      objective_completed: true,
      squad_completed: true,
      tactics_completed: true,
      status: "Completata e non archiviata"
    },
    {
      id: 2,
      team_name: "Barcelona",
      archived: true,
      objective_completed: true,
      squad_completed: false,
      tactics_completed: true,
      status: "Archiviata e parzialmente completata"
    },
    {
      id: 3,
      team_name: "Liverpool",
      archived: false,
      objective_completed: false,
      squad_completed: true,
      tactics_completed: false,
      status: "Attiva e parzialmente completata"
    },
    {
      id: 4,
      team_name: "Real Madrid",
      archived: true,
      objective_completed: true,
      squad_completed: true,
      tactics_completed: true,
      status: "Completata e archiviata"
    },
    {
      id: 5,
      team_name: "Chelsea",
      archived: false,
      objective_completed: false,
      squad_completed: false,
      tactics_completed: false,
      status: "Attiva e non completata"
    }
  ];

  // Funzione per verificare se una sfida è completata
  const isChallengeCompleted = (challenge) => {
    return challenge.objective_completed === true && 
           challenge.squad_completed === true && 
           challenge.tactics_completed === true;
  };

  // Test filtri
  console.log('\n--- SFIDE ATTIVE ---');
  const activeChallenges = sampleChallenges.filter(challenge => 
    !challenge.archived && 
    !isChallengeCompleted(challenge)
  );
  activeChallenges.forEach(c => console.log(`${c.team_name}: ${c.status}`));

  console.log('\n--- SFIDE COMPLETATE ---');
  const completedChallenges = sampleChallenges.filter(challenge => 
    !challenge.archived && 
    isChallengeCompleted(challenge)
  );
  completedChallenges.forEach(c => console.log(`${c.team_name}: ${c.status}`));

  console.log('\n--- SFIDE ARCHIVIATE ---');
  const archivedChallenges = sampleChallenges.filter(challenge => 
    challenge.archived
  );
  archivedChallenges.forEach(c => console.log(`${c.team_name}: ${c.status}`));

  console.log('\n--- RIEPILOGO ---');
  console.log(`Totale sfide: ${sampleChallenges.length}`);
  console.log(`Attive: ${activeChallenges.length}`);
  console.log(`Completate: ${completedChallenges.length}`);
  console.log(`Archiviate: ${archivedChallenges.length}`);
  
  // Verifica che non ci siano sovrapposizioni
  const totalFiltered = activeChallenges.length + completedChallenges.length + archivedChallenges.length;
  console.log(`Somma filtri: ${totalFiltered}`);
  
  if (totalFiltered !== sampleChallenges.length) {
    console.warn('⚠️ ATTENZIONE: Ci sono sovrapposizioni o sfide mancanti nei filtri!');
  } else {
    console.log('✅ Filtri corretti: nessuna sovrapposizione');
  }
}

// Test della logica API
function testAPILogic(challenges, filter) {
  console.log(`\n=== Test API Logic per filtro: ${filter} ===`);
  
  let filteredData = challenges;
  
  if (filter === 'completed') {
    // Sfide completate: tutti e 3 gli obiettivi completati e non archiviate
    filteredData = challenges.filter(challenge => 
      !challenge.archived && 
      challenge.objective_completed === true && 
      challenge.squad_completed === true && 
      challenge.tactics_completed === true
    );
  } else if (filter === 'active') {
    // Sfide attive: non archiviate e NON completate
    filteredData = challenges.filter(challenge => 
      !challenge.archived && 
      !(challenge.objective_completed === true && 
        challenge.squad_completed === true && 
        challenge.tactics_completed === true)
    );
  } else if (filter === 'archived') {
    // Sfide archiviate
    filteredData = challenges.filter(challenge => challenge.archived);
  }
  
  console.log(`Risultati per ${filter}:`, filteredData.map(c => c.team_name));
  return filteredData;
}

// Esporta le funzioni per l'uso nella console
window.debugChallengeFilters = debugChallengeFilters;
window.testAPILogic = testAPILogic;

console.log('Debug script caricato. Funzioni disponibili:');
console.log('- debugChallengeFilters(): Testa la logica di filtraggio');
console.log('- testAPILogic(challenges, filter): Testa la logica API specifica');
