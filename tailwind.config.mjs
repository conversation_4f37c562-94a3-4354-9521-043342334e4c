/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        primary: "#0e3169",
        accent: "#0e3169",
        bgMain: "#0D1117",
        bgBox: "#161B22",
        textPrimary: "#C9D1D9",
        textTitle: "#58A6FF",
        btnPrimary: "#1F6FEB",
        btnHover: "#2F81F7",
        textSecondary: "#8B949E",
        dropdownBg: "#21262D",
      },
      fontFamily: {
        inter: ["var(--font-inter)"],
      },
    },
  },
  plugins: [],
};
