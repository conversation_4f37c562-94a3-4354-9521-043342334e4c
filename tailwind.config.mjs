/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        // Palette principale ispirata al calcio
        primary: "#1B4332", // Verde scuro calcio
        accent: "#2D6A4F", // Verde medio
        secondary: "#40916C", // Verde chiaro
        tertiary: "#52B788", // Verde lime

        // Backgrounds
        bgMain: "#081C15", // Verde molto scuro
        bgBox: "#1B4332", // Verde scuro per le card
        bgHover: "#2D6A4F", // Verde per hover

        // Testi
        textPrimary: "#D8F3DC", // Verde molto chiaro
        textSecondary: "#95D5B2", // Verde chiaro
        textTitle: "#74C69D", // Verde medio per titoli
        textMuted: "#52B788", // Verde per testo secondario

        // Bottoni
        btnPrimary: "#2D6A4F", // Verde principale
        btnHover: "#40916C", // Verde hover
        btnSecondary: "#52B788", // Verde secondario

        // Dropdown e form
        dropdownBg: "#1B4332",
        inputBg: "#2D6A4F",
        inputBorder: "#40916C",

        // Stati
        success: "#52B788",
        warning: "#F77F00",
        error: "#D62828",
        info: "#457B9D",
      },
      fontFamily: {
        inter: ["var(--font-inter)"],
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'bounce-subtle': 'bounceSubtle 2s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        bounceSubtle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-5px)' },
        },
      },
      boxShadow: {
        'glow': '0 0 20px rgba(45, 106, 79, 0.3)',
        'glow-lg': '0 0 30px rgba(45, 106, 79, 0.4)',
      },
    },
  },
  plugins: [],
};
