// Debug script per pulire i dati dei cookie corrotti
// Esegui questo script nella console del browser se hai problemi con i cookie

function debugCookieConsent() {
  console.log('=== Cookie Consent Debug ===');
  
  // Controlla se localStorage è disponibile
  if (typeof localStorage === 'undefined') {
    console.error('localStorage non disponibile');
    return;
  }
  
  // Ottieni i dati attuali
  const currentData = localStorage.getItem('cookie-consent');
  console.log('Dati attuali:', currentData);
  
  if (!currentData) {
    console.log('Nessun dato di consenso trovato');
    return;
  }
  
  // Prova a parsare i dati
  try {
    const parsed = JSON.parse(currentData);
    console.log('Dati parsati con successo:', parsed);
    
    // Valida la struttura
    if (typeof parsed === 'object' && parsed !== null) {
      console.log('Struttura valida');
      console.log('Analytics:', parsed.analytics);
      console.log('Advertising:', parsed.advertising);
      console.log('Functional:', parsed.functional);
      console.log('Timestamp:', new Date(parsed.timestamp));
    } else {
      console.warn('Struttura non valida');
    }
  } catch (error) {
    console.error('Errore nel parsing:', error);
    console.log('Dati corrotti rilevati');
  }
}

function clearCookieConsent() {
  console.log('Pulizia dati cookie consent...');
  localStorage.removeItem('cookie-consent');
  console.log('Dati puliti. Ricarica la pagina per vedere il banner.');
}

function setCookieConsent(analytics = false, advertising = false) {
  const consent = {
    analytics: analytics,
    advertising: advertising,
    functional: true,
    timestamp: Date.now()
  };
  
  localStorage.setItem('cookie-consent', JSON.stringify(consent));
  console.log('Consenso impostato:', consent);
}

// Esporta le funzioni per l'uso nella console
window.debugCookieConsent = debugCookieConsent;
window.clearCookieConsent = clearCookieConsent;
window.setCookieConsent = setCookieConsent;

console.log('Debug script caricato. Funzioni disponibili:');
console.log('- debugCookieConsent(): Analizza i dati attuali');
console.log('- clearCookieConsent(): Pulisce i dati corrotti');
console.log('- setCookieConsent(analytics, advertising): Imposta consenso manualmente');
