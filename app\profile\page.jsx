'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useLanguage } from '@/lib/LanguageContext';
import LanguageSelector from '@/components/LanguageSelector';

export default function ProfilePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { t } = useLanguage();
  
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  if (status === 'loading') {
    return <div className="container mx-auto p-4 text-center">Loading...</div>;
  }

  if (!session) {
    return null;
  }

  return (
    <div className="container mx-auto p-4 max-w-2xl">
      <h1 className="text-2xl font-bold mb-6 text-textTitle">{t.profile}</h1>
      
      <div className="card mb-6">
        <div className="flex items-center space-x-4 mb-4">
          {session.user.image ? (
            <img 
              src={session.user.image} 
              alt={session.user.name || 'User'} 
              className="w-16 h-16 rounded-full"
            />
          ) : (
            <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center text-white text-xl">
              {session.user.name?.charAt(0) || 'U'}
            </div>
          )}
          <div>
            <h2 className="text-xl font-semibold">{session.user.name}</h2>
            <p className="text-textSecondary">{session.user.email}</p>
          </div>
        </div>
      </div>
      
      <div className="card">
        <h2 className="text-xl font-semibold mb-4">{t.language}</h2>
        <LanguageSelector />
      </div>
    </div>
  );
}

