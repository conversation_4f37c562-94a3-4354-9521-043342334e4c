'use client';

import { useState, useEffect } from 'react';
import { useLanguage } from '../lib/LanguageContext';

const CookieBanner = () => {
  const { t, language } = useLanguage();
  const [showBanner, setShowBanner] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    // Check if user has already made a choice
    const consent = localStorage.getItem('cookie-consent');
    if (!consent) {
      setShowBanner(true);
    } else {
      // Apply saved consent
      const consentData = JSON.parse(consent);
      updateGoogleConsent(consentData);
    }
  }, []);

  const updateGoogleConsent = (consentData) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('consent', 'update', {
        'analytics_storage': consentData.analytics ? 'granted' : 'denied',
        'ad_storage': consentData.advertising ? 'granted' : 'denied',
        'ad_user_data': consentData.advertising ? 'granted' : 'denied',
        'ad_personalization': consentData.advertising ? 'granted' : 'denied',
        'functionality_storage': 'granted',
        'security_storage': 'granted'
      });
    }
  };

  const handleAcceptAll = () => {
    const consent = {
      analytics: true,
      advertising: true,
      functional: true,
      timestamp: Date.now()
    };
    
    localStorage.setItem('cookie-consent', JSON.stringify(consent));
    updateGoogleConsent(consent);
    setShowBanner(false);
  };

  const handleRejectAll = () => {
    const consent = {
      analytics: false,
      advertising: false,
      functional: true,
      timestamp: Date.now()
    };
    
    localStorage.setItem('cookie-consent', JSON.stringify(consent));
    updateGoogleConsent(consent);
    setShowBanner(false);
  };

  const handleCustomize = (customConsent) => {
    const consent = {
      ...customConsent,
      functional: true, // Always required
      timestamp: Date.now()
    };
    
    localStorage.setItem('cookie-consent', JSON.stringify(consent));
    updateGoogleConsent(consent);
    setShowBanner(false);
    setShowDetails(false);
  };

  if (!showBanner) return null;

  const cookieTexts = {
    it: {
      title: "Utilizziamo i cookie",
      description: "Utilizziamo cookie e tecnologie simili per migliorare la tua esperienza, analizzare il traffico e personalizzare i contenuti. Puoi scegliere quali cookie accettare.",
      acceptAll: "Accetta tutti",
      rejectAll: "Rifiuta tutti",
      customize: "Personalizza",
      save: "Salva preferenze",
      necessary: "Cookie necessari",
      necessaryDesc: "Essenziali per il funzionamento del sito",
      analytics: "Cookie analitici",
      analyticsDesc: "Ci aiutano a capire come utilizzi il sito",
      advertising: "Cookie pubblicitari",
      advertisingDesc: "Utilizzati per mostrare annunci pertinenti"
    },
    en: {
      title: "We use cookies",
      description: "We use cookies and similar technologies to improve your experience, analyze traffic and personalize content. You can choose which cookies to accept.",
      acceptAll: "Accept all",
      rejectAll: "Reject all",
      customize: "Customize",
      save: "Save preferences",
      necessary: "Necessary cookies",
      necessaryDesc: "Essential for the website to function",
      analytics: "Analytics cookies",
      analyticsDesc: "Help us understand how you use the site",
      advertising: "Advertising cookies",
      advertisingDesc: "Used to show relevant ads"
    }
  };

  const texts = cookieTexts[language] || cookieTexts.it;

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black/50 z-40" />
      
      {/* Banner */}
      <div className="fixed bottom-0 left-0 right-0 bg-bgBox border-t border-textSecondary/20 p-4 md:p-6 z-50">
        <div className="max-w-6xl mx-auto">
          {!showDetails ? (
            <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-textPrimary mb-2">
                  {texts.title}
                </h3>
                <p className="text-textSecondary text-sm">
                  {texts.description}
                </p>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-2 w-full md:w-auto">
                <button
                  onClick={handleRejectAll}
                  className="px-4 py-2 text-textSecondary border border-textSecondary/30 rounded-md hover:bg-textSecondary/10 transition-colors"
                >
                  {texts.rejectAll}
                </button>
                <button
                  onClick={() => setShowDetails(true)}
                  className="px-4 py-2 text-accent border border-accent rounded-md hover:bg-accent/10 transition-colors"
                >
                  {texts.customize}
                </button>
                <button
                  onClick={handleAcceptAll}
                  className="px-4 py-2 bg-accent text-white rounded-md hover:bg-accent/90 transition-colors"
                >
                  {texts.acceptAll}
                </button>
              </div>
            </div>
          ) : (
            <CookieDetails 
              texts={texts} 
              onSave={handleCustomize}
              onBack={() => setShowDetails(false)}
            />
          )}
        </div>
      </div>
    </>
  );
};

const CookieDetails = ({ texts, onSave, onBack }) => {
  const [preferences, setPreferences] = useState({
    analytics: false,
    advertising: false
  });

  const handleSave = () => {
    onSave(preferences);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-textPrimary">
          {texts.customize}
        </h3>
        <button
          onClick={onBack}
          className="text-textSecondary hover:text-textPrimary"
        >
          ←
        </button>
      </div>
      
      <div className="space-y-3">
        {/* Necessary cookies - always enabled */}
        <div className="flex items-center justify-between p-3 bg-bgMain rounded-md">
          <div>
            <h4 className="font-medium text-textPrimary">{texts.necessary}</h4>
            <p className="text-sm text-textSecondary">{texts.necessaryDesc}</p>
          </div>
          <div className="text-green-500 font-medium">ON</div>
        </div>
        
        {/* Analytics cookies */}
        <div className="flex items-center justify-between p-3 bg-bgMain rounded-md">
          <div>
            <h4 className="font-medium text-textPrimary">{texts.analytics}</h4>
            <p className="text-sm text-textSecondary">{texts.analyticsDesc}</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={preferences.analytics}
              onChange={(e) => setPreferences(prev => ({ ...prev, analytics: e.target.checked }))}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-textSecondary/30 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-accent"></div>
          </label>
        </div>
        
        {/* Advertising cookies */}
        <div className="flex items-center justify-between p-3 bg-bgMain rounded-md">
          <div>
            <h4 className="font-medium text-textPrimary">{texts.advertising}</h4>
            <p className="text-sm text-textSecondary">{texts.advertisingDesc}</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={preferences.advertising}
              onChange={(e) => setPreferences(prev => ({ ...prev, advertising: e.target.checked }))}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-textSecondary/30 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-accent"></div>
          </label>
        </div>
      </div>
      
      <div className="flex gap-2 pt-4">
        <button
          onClick={handleSave}
          className="flex-1 px-4 py-2 bg-accent text-white rounded-md hover:bg-accent/90 transition-colors"
        >
          {texts.save}
        </button>
      </div>
    </div>
  );
};

export default CookieBanner;
