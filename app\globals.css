@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #081C15;
  --foreground: #D8F3DC;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Inter', sans-serif;
  scroll-behavior: smooth;
}

/* Scrollbar personalizzata */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #081C15;
}

::-webkit-scrollbar-thumb {
  background: #2D6A4F;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #40916C;
}

@layer components {
  .btn-primary {
    @apply bg-btnPrimary text-textPrimary py-3 px-6 rounded-lg hover:bg-btnHover transition-all duration-200 font-medium shadow-lg hover:shadow-glow transform hover:scale-105;
  }

  .btn-secondary {
    @apply bg-btnSecondary text-bgMain py-3 px-6 rounded-lg hover:bg-tertiary transition-all duration-200 font-medium shadow-lg hover:shadow-glow transform hover:scale-105;
  }

  .select-custom {
    @apply bg-inputBg text-textPrimary border border-inputBorder rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-accent focus:border-accent transition-all duration-200;
  }

  .card {
    @apply bg-bgBox p-6 rounded-xl border border-accent/20 shadow-lg hover:shadow-glow transition-all duration-300 backdrop-blur-sm;
  }

  .card-hover {
    @apply hover:bg-bgHover hover:border-accent/40 transform hover:scale-[1.02];
  }

  .input-field {
    @apply bg-inputBg text-textPrimary border border-inputBorder rounded-lg p-3 focus:outline-none focus:ring-2 focus:ring-accent focus:border-accent transition-all duration-200 placeholder-textMuted;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-textTitle to-tertiary bg-clip-text text-transparent;
  }

  .glass-effect {
    @apply backdrop-blur-md bg-bgBox/80 border border-accent/20;
  }
}
