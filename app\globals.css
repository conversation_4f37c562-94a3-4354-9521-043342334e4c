@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #0D1117;
  --foreground: #C9D1D9;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Inter', sans-serif;
}

@layer components {
  .btn-primary {
    @apply bg-btnPrimary text-white py-2 px-4 rounded-md hover:bg-btnHover transition-colors duration-200;
  }

  .select-custom {
    @apply bg-dropdownBg text-textPrimary border border-textSecondary/20 rounded-md p-2 focus:outline-none focus:ring-2 focus:ring-accent;
  }

  .card {
    @apply bg-bgBox p-4 rounded-lg border border-textSecondary/10 shadow-md;
  }
}
