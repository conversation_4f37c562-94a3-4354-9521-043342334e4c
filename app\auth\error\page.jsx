'use client';

import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Suspense } from 'react';

function ErrorContent() {
  const searchParams = useSearchParams();
  const error = searchParams.get('error');

  let errorMessage = 'Si è verificato un errore durante l\'autenticazione';

  if (error === 'OAuthSignin') errorMessage = 'Errore durante l\'avvio del processo OAuth';
  if (error === 'OAuthCallback') errorMessage = 'Errore durante la callback OAuth';
  if (error === 'OAuthCreateAccount') errorMessage = 'Errore durante la creazione dell\'account OAuth';
  if (error === 'EmailCreateAccount') errorMessage = 'Errore durante la creazione dell\'account email';
  if (error === 'Callback') errorMessage = 'Errore durante la callback';
  if (error === 'OAuthAccountNotLinked') errorMessage = 'L\'email è già associata a un altro account';
  if (error === 'EmailSignin') errorMessage = 'Errore durante l\'invio dell\'email di accesso';
  if (error === 'CredentialsSignin') errorMessage = 'Credenziali non valide';
  if (error === 'SessionRequired') errorMessage = 'Accesso richiesto per visualizzare questa pagina';

  return (
    <div className="min-h-screen bg-bgMain flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8 card p-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-textTitle">Errore di autenticazione</h2>
          <div className="mt-4 bg-red-900/20 border border-red-500/50 text-red-200 p-4 rounded-md">
            {errorMessage}
          </div>
          <div className="mt-6">
            <Link href="/auth/signin" className="btn-primary inline-block">
              Torna al login
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ErrorPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-bgMain flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8 card p-8">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-textTitle">Caricamento...</h2>
          </div>
        </div>
      </div>
    }>
      <ErrorContent />
    </Suspense>
  );
}
