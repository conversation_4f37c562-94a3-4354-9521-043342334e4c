'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { toast } from 'react-hot-toast';

export default function SavedChallengesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [challenges, setChallenges] = useState([]);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  // Funzione per aggiornare lo stato di completamento di una sfida
  const toggleChallengeCompletion = async (challengeId, field, currentValue) => {
    if (updating) return;

    setUpdating(true);

    try {
      const response = await fetch('/api/challenges/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          challengeId,
          field,
          value: !currentValue,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Errore nell\'aggiornamento della sfida');
      }

      // Aggiorna lo stato locale
      setChallenges(prevChallenges =>
        prevChallenges.map(challenge =>
          challenge.id === challengeId
            ? { ...challenge, [field]: !currentValue }
            : challenge
        )
      );

      toast.success('Sfida aggiornata!');
    } catch (error) {
      console.error('Error updating challenge:', error);
      toast.error(error.message || 'Errore nell\'aggiornamento della sfida');
    } finally {
      setUpdating(false);
    }
  };

  useEffect(() => {
    const fetchSavedChallenges = async () => {
      if (status !== 'authenticated') return;

      try {
        const response = await fetch('/api/challenges/saved');
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Errore nel recupero delle sfide salvate');
        }

        setChallenges(data.challenges || []);
      } catch (error) {
        console.error('Error fetching saved challenges:', error);
        setError(error.message || 'Errore nel recupero delle sfide salvate');
      } finally {
        setLoading(false);
      }
    };

    fetchSavedChallenges();
  }, [status]);

  // Formatta la data in formato leggibile
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('it-IT', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-bgMain flex items-center justify-center">
        <div className="text-center">
          <div className="h-12 w-12 border-4 border-accent border-t-transparent rounded-full animate-spin mx-auto"></div>
          <p className="mt-4 text-textPrimary">Caricamento...</p>
        </div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-bgMain py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8 flex items-center justify-between">
          <h1 className="text-2xl font-bold text-textTitle">Le mie sfide salvate</h1>
          <Link href="/profile" className="text-accent hover:underline flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            Torna al profilo
          </Link>
        </div>

        {error && (
          <div className="bg-red-900/20 border border-red-500/50 text-red-200 p-4 rounded-md mb-6">
            <p>{error}</p>
          </div>
        )}

        {challenges.length === 0 && !loading && !error ? (
          <div className="card p-8 text-center">
            <p className="text-textPrimary mb-4">Non hai ancora salvato nessuna sfida.</p>
            <Link href="/" className="btn-primary inline-block">
              Genera una nuova sfida
            </Link>
          </div>
        ) : (
          <div className="space-y-6">
            {challenges.map((challenge) => (
              <div key={challenge.id} className="card p-6">
                <div className="flex flex-col md:flex-row md:items-center mb-4">
                  <div className="flex items-center mb-4 md:mb-0 md:mr-6">
                    <div className="relative w-16 h-16 flex-shrink-0 mr-4">
                      {challenge.team_logo && (
                        <Image
                          src={challenge.team_logo}
                          alt={`Logo ${challenge.team_name}`}
                          fill
                          className="object-contain"
                        />
                      )}
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold text-textTitle">{challenge.team_name}</h2>
                      <p className="text-textSecondary text-sm">
                        {challenge.team_country} - {challenge.team_league}
                      </p>
                    </div>
                  </div>
                  <div className="md:ml-auto text-textSecondary text-sm">
                    Salvata il {formatDate(challenge.created_at)}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                  <div
                    className={`p-3 rounded-md cursor-pointer transition-all duration-200 ${
                      challenge.objective_completed
                        ? 'bg-green-900/20 border border-green-500/30'
                        : 'bg-bgBox/50 hover:bg-bgBox'
                    }`}
                    onClick={() => toggleChallengeCompletion(challenge.id, 'objective_completed', challenge.objective_completed)}
                  >
                    <h3 className="font-medium text-textTitle mb-2 flex items-center">
                      <span className="mr-2">
                        {challenge.objective_completed ? '✅' : '⬜'}
                      </span>
                      Obiettivo
                    </h3>
                    <p className={`text-textPrimary text-sm ${challenge.objective_completed ? 'line-through opacity-70' : ''}`}>
                      {challenge.objective_text}
                    </p>
                  </div>

                  <div
                    className={`p-3 rounded-md cursor-pointer transition-all duration-200 ${
                      challenge.squad_completed
                        ? 'bg-green-900/20 border border-green-500/30'
                        : 'bg-bgBox/50 hover:bg-bgBox'
                    }`}
                    onClick={() => toggleChallengeCompletion(challenge.id, 'squad_completed', challenge.squad_completed)}
                  >
                    <h3 className="font-medium text-textTitle mb-2 flex items-center">
                      <span className="mr-2">
                        {challenge.squad_completed ? '✅' : '⬜'}
                      </span>
                      Rosa
                    </h3>
                    <p className={`text-textPrimary text-sm ${challenge.squad_completed ? 'line-through opacity-70' : ''}`}>
                      {challenge.squad_text}
                    </p>
                  </div>

                  <div
                    className={`p-3 rounded-md cursor-pointer transition-all duration-200 ${
                      challenge.tactics_completed
                        ? 'bg-green-900/20 border border-green-500/30'
                        : 'bg-bgBox/50 hover:bg-bgBox'
                    }`}
                    onClick={() => toggleChallengeCompletion(challenge.id, 'tactics_completed', challenge.tactics_completed)}
                  >
                    <h3 className="font-medium text-textTitle mb-2 flex items-center">
                      <span className="mr-2">
                        {challenge.tactics_completed ? '✅' : '⬜'}
                      </span>
                      Tattica
                    </h3>
                    <p className={`text-textPrimary text-sm ${challenge.tactics_completed ? 'line-through opacity-70' : ''}`}>
                      {challenge.tactics_text}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
